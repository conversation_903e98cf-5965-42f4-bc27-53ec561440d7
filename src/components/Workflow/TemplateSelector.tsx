/**
 * Template Selector Component with Agent Consultation Features
 * 
 * Enhanced template selection with agent consultation capabilities
 */

'use client';

import { useState, useEffect } from 'react';

interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  difficulty: 'easy' | 'medium' | 'hard';
  estimatedTime: number;
  featured: boolean;
  consultationEnabled: boolean;
  agentCount: number;
  steps: Array<{
    id: string;
    name: string;
    type: string;
    dependencies?: string[];
    consultationConfig?: {
      enabled: boolean;
      triggers?: Array<{
        type: string;
        agents: string[];
        priority: string;
      }>;
      maxConsultations?: number;
      timeoutMs?: number;
      fallbackBehavior?: string;
    };
  }>;
}

interface Props {
  onTemplateSelect: (template: WorkflowTemplate) => void;
  showAgentFeatures?: boolean;
  filterByAgentSupport?: boolean;
}

export default function TemplateSelector({ 
  onTemplateSelect, 
  showAgentFeatures = true, 
  filterByAgentSupport = false 
}: Props) {
  const [templates, setTemplates] = useState<WorkflowTemplate[]>([]);
  const [filteredTemplates, setFilteredTemplates] = useState<WorkflowTemplate[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [showAgentOnly, setShowAgentOnly] = useState(filterByAgentSupport);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadTemplates();
  }, []);

  useEffect(() => {
    filterTemplates();
  }, [templates, selectedCategory, selectedDifficulty, searchQuery, showAgentOnly]);

  const loadTemplates = async () => {
    try {
      setIsLoading(true);
      
      // Mock templates with agent consultation features
      const mockTemplates: WorkflowTemplate[] = [
        {
          id: 'blog-post-seo',
          name: 'SEO Blog Post',
          description: 'Complete SEO-optimized blog post generation with keyword research and agent consultation',
          category: 'blog',
          tags: ['seo', 'blog', 'content-marketing'],
          difficulty: 'easy',
          estimatedTime: 45,
          featured: true,
          consultationEnabled: true,
          agentCount: 3,
          steps: [
            { id: 'topic-input', name: 'Topic Input', type: 'TEXT_INPUT', dependencies: [] },
            {
              id: 'keyword-research',
              name: 'Keyword Research',
              type: 'AI_GENERATION',
              dependencies: ['topic-input'],
              consultationConfig: {
                enabled: true,
                triggers: [
                  {
                    type: 'always',
                    agents: ['seo-keyword', 'market-research'],
                    priority: 'high'
                  }
                ],
                maxConsultations: 3,
                timeoutMs: 30000,
                fallbackBehavior: 'continue'
              }
            },
            {
              id: 'content-creation',
              name: 'Content Creation',
              type: 'AI_GENERATION',
              dependencies: ['keyword-research'],
              consultationConfig: {
                enabled: true,
                triggers: [
                  {
                    type: 'always',
                    agents: ['seo-keyword', 'content-strategy'],
                    priority: 'high'
                  }
                ],
                maxConsultations: 3,
                timeoutMs: 30000,
                fallbackBehavior: 'continue'
              }
            },
            { id: 'human-review', name: 'Human Review', type: 'HUMAN_REVIEW', dependencies: ['content-creation'] }
          ]
        },
        {
          id: 'product-descriptions',
          name: 'Bulk Product Descriptions',
          description: 'Generate product descriptions in bulk with brand voice consistency and market insights',
          category: 'ecommerce',
          tags: ['ecommerce', 'bulk', 'product-descriptions'],
          difficulty: 'medium',
          estimatedTime: 120,
          featured: true,
          consultationEnabled: true,
          agentCount: 2,
          steps: [
            { id: 'csv-import', name: 'Import Product Data', type: 'CSV_IMPORT', dependencies: [] },
            { id: 'brand-voice-input', name: 'Brand Voice Guidelines', type: 'TEXT_INPUT', dependencies: [] },
            {
              id: 'bulk-generation',
              name: 'Generate Descriptions',
              type: 'LOOP',
              dependencies: ['csv-import', 'brand-voice-input'],
              consultationConfig: {
                enabled: true,
                triggers: [
                  {
                    type: 'always',
                    agents: ['market-research', 'content-strategy'],
                    priority: 'high'
                  }
                ],
                maxConsultations: 3,
                timeoutMs: 30000,
                fallbackBehavior: 'continue'
              }
            }
          ]
        },
        {
          id: 'content-refresh',
          name: 'Content Refresh & Update',
          description: 'Analyze and update existing content for improved SEO and relevance with agent insights',
          category: 'seo',
          tags: ['content-refresh', 'seo', 'optimization'],
          difficulty: 'medium',
          estimatedTime: 60,
          featured: false,
          consultationEnabled: true,
          agentCount: 2,
          steps: [
            { id: 'url-input', name: 'Content URL Input', type: 'TEXT_INPUT', dependencies: [] },
            { id: 'content-fetch', name: 'Fetch Existing Content', type: 'URL_FETCH', dependencies: ['url-input'] },
            {
              id: 'content-analysis',
              name: 'Analyze Current Content',
              type: 'AI_GENERATION',
              dependencies: ['content-fetch'],
              consultationConfig: {
                enabled: true,
                triggers: [
                  {
                    type: 'always',
                    agents: ['seo-keyword', 'content-strategy'],
                    priority: 'high'
                  }
                ],
                maxConsultations: 3,
                timeoutMs: 30000,
                fallbackBehavior: 'continue'
              }
            }
          ]
        },
        {
          id: 'social-media-campaign',
          name: 'Social Media Campaign',
          description: 'Create comprehensive social media content across multiple platforms',
          category: 'social',
          tags: ['social-media', 'campaign', 'multi-platform'],
          difficulty: 'easy',
          estimatedTime: 30,
          featured: false,
          consultationEnabled: false,
          agentCount: 0,
          steps: [
            { id: 'campaign-input', name: 'Campaign Details', type: 'TEXT_INPUT', dependencies: [] },
            { id: 'content-generation', name: 'Generate Posts', type: 'AI_GENERATION', dependencies: ['campaign-input'] }
          ]
        }
      ];

      setTemplates(mockTemplates);
    } catch (error) {
      console.error('Failed to load templates:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const filterTemplates = () => {
    let filtered = templates;

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(template => template.category === selectedCategory);
    }

    // Filter by difficulty
    if (selectedDifficulty !== 'all') {
      filtered = filtered.filter(template => template.difficulty === selectedDifficulty);
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(template =>
        template.name.toLowerCase().includes(query) ||
        template.description.toLowerCase().includes(query) ||
        template.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // Filter by agent support
    if (showAgentOnly) {
      filtered = filtered.filter(template => template.consultationEnabled);
    }

    setFilteredTemplates(filtered);
  };

  const getCategories = () => {
    const categories = Array.from(new Set(templates.map(t => t.category)));
    return ['all', ...categories];
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'hard': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'blog': return '📝';
      case 'ecommerce': return '🛒';
      case 'seo': return '🔍';
      case 'social': return '📱';
      default: return '📋';
    }
  };

  const getAgentIcons = (agentCount: number) => {
    const icons = [];
    for (let i = 0; i < Math.min(agentCount, 3); i++) {
      icons.push('🤖');
    }
    return icons.join('');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Loading templates...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filters */}
      <div className="flex flex-wrap items-center gap-4">
        {/* Search */}
        <div className="flex-1 min-w-64">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <input
              type="text"
              placeholder="Search templates..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>

        {/* Category Filter */}
        <select
          value={selectedCategory}
          onChange={(e) => setSelectedCategory(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
        >
          {getCategories().map(category => (
            <option key={category} value={category}>
              {category === 'all' ? 'All Categories' : category.charAt(0).toUpperCase() + category.slice(1)}
            </option>
          ))}
        </select>

        {/* Difficulty Filter */}
        <select
          value={selectedDifficulty}
          onChange={(e) => setSelectedDifficulty(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="all">All Difficulties</option>
          <option value="easy">Easy</option>
          <option value="medium">Medium</option>
          <option value="hard">Hard</option>
        </select>

        {/* Agent Filter */}
        {showAgentFeatures && (
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={showAgentOnly}
              onChange={(e) => setShowAgentOnly(e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="text-sm text-gray-700">Agent-Enhanced Only</span>
          </label>
        )}
      </div>

      {/* Results Count */}
      <div className="text-sm text-gray-600">
        Showing {filteredTemplates.length} of {templates.length} templates
        {showAgentOnly && ' with agent consultation'}
      </div>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredTemplates.map((template) => (
          <div
            key={template.id}
            className="bg-white rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all cursor-pointer"
            onClick={() => onTemplateSelect(template)}
          >
            <div className="p-6">
              {/* Header */}
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <span className="text-2xl">{getCategoryIcon(template.category)}</span>
                  <div>
                    <h3 className="font-semibold text-gray-900">{template.name}</h3>
                    {template.featured && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        Featured
                      </span>
                    )}
                  </div>
                </div>
                
                {template.consultationEnabled && showAgentFeatures && (
                  <div className="flex items-center space-x-1">
                    <span className="text-sm">{getAgentIcons(template.agentCount)}</span>
                    <span className="text-xs text-blue-600 font-medium">{template.agentCount}</span>
                  </div>
                )}
              </div>

              {/* Description */}
              <p className="text-gray-600 text-sm mb-4 line-clamp-3">{template.description}</p>

              {/* Agent Consultation Badge */}
              {template.consultationEnabled && showAgentFeatures && (
                <div className="mb-4">
                  <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    <span className="mr-1">🤖</span>
                    Agent-Enhanced
                  </div>
                </div>
              )}

              {/* Metadata */}
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center space-x-3">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(template.difficulty)}`}>
                    {template.difficulty}
                  </span>
                  <span className="text-gray-500">
                    ~{template.estimatedTime}min
                  </span>
                </div>
                
                <div className="text-gray-500">
                  {template.steps.length} steps
                </div>
              </div>

              {/* Tags */}
              <div className="mt-3 flex flex-wrap gap-1">
                {template.tags.slice(0, 3).map((tag) => (
                  <span
                    key={tag}
                    className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800"
                  >
                    {tag}
                  </span>
                ))}
                {template.tags.length > 3 && (
                  <span className="text-xs text-gray-500">
                    +{template.tags.length - 3} more
                  </span>
                )}
              </div>

              {/* Agent Steps Preview */}
              {template.consultationEnabled && showAgentFeatures && (
                <div className="mt-4 pt-3 border-t border-gray-100">
                  <div className="text-xs text-gray-600 mb-2">Agent-Enhanced Steps:</div>
                  <div className="space-y-1">
                    {template.steps
                      .filter(step => step.consultationConfig?.enabled)
                      .slice(0, 2)
                      .map((step) => (
                        <div key={step.id} className="flex items-center space-x-2 text-xs">
                          <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                          <span className="text-gray-700">{step.name}</span>
                          <span className="text-blue-600">
                            ({step.consultationConfig?.triggers?.[0]?.agents?.length || 0} agents)
                          </span>
                        </div>
                      ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {filteredTemplates.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No Templates Found</h3>
          <p className="text-gray-600 mb-4">
            Try adjusting your filters or search terms to find templates.
          </p>
          <button
            onClick={() => {
              setSelectedCategory('all');
              setSelectedDifficulty('all');
              setSearchQuery('');
              setShowAgentOnly(false);
            }}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Clear Filters
          </button>
        </div>
      )}
    </div>
  );
}
